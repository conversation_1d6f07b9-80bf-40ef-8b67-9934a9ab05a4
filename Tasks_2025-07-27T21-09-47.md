[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:Análise e Configuração Inicial DESCRIPTION:Analisar estrutura atual da API, escolher biblioteca Swagger adequada (swaggo/swag) e configurar dependências
-[ ] NAME:Configuração do Swagger DESCRIPTION:Instalar swaggo/swag, configurar main.go com anotações Swagger básicas e criar estrutura de documentação
--[ ] NAME:Instalar dependências Swagger DESCRIPTION:Executar go get para instalar github.com/swaggo/http-swagger e github.com/alecthomas/template
--[ ] NAME:Configurar anotações principais no main.go DESCRIPTION:Adicionar anotações @title, @version, @description, @host, @BasePath no main.go
--[ ] NAME:Configurar rota Swagger no router DESCRIPTION:Adicionar rota /swagger/* no router.go para servir a interface Swagger UI
--[ ] NAME:Criar estrutura docs/ DESCRIPTION:Preparar estrutura de diretório docs/ para os arquivos gerados pelo swag
-[ ] NAME:Documentação - Health Check DESCRIPTION:Documentar endpoint GET /health com anotações Swagger completas
--[ ] NAME:Documentar GET /health DESCRIPTION:Adicionar anotações Swagger completas para o endpoint de health check
-[ ] NAME:Documentação - Sessões DESCRIPTION:Documentar todos os endpoints de sessões (/sessions/*) com exemplos de request/response
--[ ] NAME:Documentar POST /sessions/add DESCRIPTION:Documentar endpoint para criar nova sessão com exemplos de request/response
--[x] NAME:Documentar GET /sessions/list DESCRIPTION:Documentar endpoint para listar todas as sessões
--[/] NAME:Documentar GET /sessions/{sessionID} DESCRIPTION:Documentar endpoint para obter sessão específica
--[ ] NAME:Documentar DELETE /sessions/{sessionID} DESCRIPTION:Documentar endpoint para deletar sessão
--[ ] NAME:Documentar POST /sessions/{sessionID}/connect DESCRIPTION:Documentar endpoint para conectar sessão
--[ ] NAME:Documentar POST /sessions/{sessionID}/logout DESCRIPTION:Documentar endpoint para logout da sessão
--[ ] NAME:Documentar GET /sessions/{sessionID}/status DESCRIPTION:Documentar endpoint para obter status da sessão
--[ ] NAME:Documentar GET /sessions/{sessionID}/qr DESCRIPTION:Documentar endpoint para obter QR Code
--[ ] NAME:Documentar POST /sessions/{sessionID}/pairphone DESCRIPTION:Documentar endpoint para pareamento por telefone
--[ ] NAME:Documentar POST /sessions/{sessionID}/proxy/set DESCRIPTION:Documentar endpoint para configurar proxy
-[ ] NAME:Documentação - Mensagens DESCRIPTION:Documentar todos os endpoints de mensagens (/messages/*) incluindo diferentes tipos de mídia
--[ ] NAME:Documentar POST /messages/{sessionID}/send/text DESCRIPTION:Documentar endpoint para envio de mensagem de texto
--[ ] NAME:Documentar POST /messages/{sessionID}/send/media DESCRIPTION:Documentar endpoint para envio de mídia genérica
--[ ] NAME:Documentar POST /messages/{sessionID}/send/image DESCRIPTION:Documentar endpoint para envio de imagem
--[ ] NAME:Documentar POST /messages/{sessionID}/send/audio DESCRIPTION:Documentar endpoint para envio de áudio
--[ ] NAME:Documentar POST /messages/{sessionID}/send/video DESCRIPTION:Documentar endpoint para envio de vídeo
--[ ] NAME:Documentar POST /messages/{sessionID}/send/document DESCRIPTION:Documentar endpoint para envio de documento
--[ ] NAME:Documentar POST /messages/{sessionID}/send/location DESCRIPTION:Documentar endpoint para envio de localização
--[ ] NAME:Documentar POST /messages/{sessionID}/send/contact DESCRIPTION:Documentar endpoint para envio de contato
--[ ] NAME:Documentar POST /messages/{sessionID}/send/sticker DESCRIPTION:Documentar endpoint para envio de sticker
--[ ] NAME:Documentar POST /messages/{sessionID}/send/buttons DESCRIPTION:Documentar endpoint para envio de mensagem com botões
--[ ] NAME:Documentar POST /messages/{sessionID}/send/list DESCRIPTION:Documentar endpoint para envio de mensagem com lista
--[ ] NAME:Documentar POST /messages/{sessionID}/send/poll DESCRIPTION:Documentar endpoint para envio de enquete
--[ ] NAME:Documentar POST /messages/{sessionID}/send/edit DESCRIPTION:Documentar endpoint para edição de mensagem
--[ ] NAME:Documentar POST /messages/{sessionID}/delete DESCRIPTION:Documentar endpoint para deletar mensagem
--[ ] NAME:Documentar POST /messages/{sessionID}/react DESCRIPTION:Documentar endpoint para reagir a mensagem
-[ ] NAME:Documentação - Chat DESCRIPTION:Documentar endpoints de chat (/chat/*) para operações específicas de gerenciamento
--[ ] NAME:Documentar POST /chat/{sessionID}/presence DESCRIPTION:Documentar endpoint para enviar presença no chat
--[ ] NAME:Documentar POST /chat/{sessionID}/markread DESCRIPTION:Documentar endpoint para marcar mensagem como lida
--[ ] NAME:Documentar POST /chat/{sessionID}/downloadimage DESCRIPTION:Documentar endpoint para download de imagem
--[ ] NAME:Documentar POST /chat/{sessionID}/downloadvideo DESCRIPTION:Documentar endpoint para download de vídeo
--[ ] NAME:Documentar POST /chat/{sessionID}/downloadaudio DESCRIPTION:Documentar endpoint para download de áudio
--[ ] NAME:Documentar POST /chat/{sessionID}/downloaddocument DESCRIPTION:Documentar endpoint para download de documento
-[ ] NAME:Documentação - Grupos DESCRIPTION:Documentar todos os endpoints de grupos (/groups/*) incluindo configurações e participantes
--[ ] NAME:Documentar POST /groups/{sessionID}/create DESCRIPTION:Documentar endpoint para criar grupo
--[ ] NAME:Documentar GET /groups/{sessionID}/list DESCRIPTION:Documentar endpoint para listar grupos
--[ ] NAME:Documentar GET /groups/{sessionID}/info DESCRIPTION:Documentar endpoint para obter informações do grupo
--[ ] NAME:Documentar POST /groups/{sessionID}/leave DESCRIPTION:Documentar endpoint para sair do grupo
--[ ] NAME:Documentar POST /groups/{sessionID}/participants/update DESCRIPTION:Documentar endpoint para atualizar participantes
--[ ] NAME:Documentar endpoints de configurações do grupo DESCRIPTION:Documentar endpoints /settings/* (name, topic, photo, announce, locked, disappearing)
--[ ] NAME:Documentar endpoints de convites DESCRIPTION:Documentar endpoints /invite/* (link, join, info)
-[ ] NAME:Geração e Validação DESCRIPTION:Gerar documentação Swagger, configurar endpoint /swagger/* e validar toda a documentação
--[ ] NAME:Gerar documentação com swag init DESCRIPTION:Executar comando swag init para gerar arquivos docs/
--[ ] NAME:Configurar endpoint /swagger/* DESCRIPTION:Configurar rota para servir Swagger UI
--[ ] NAME:Validar documentação gerada DESCRIPTION:Verificar se todos os endpoints estão documentados corretamente
-[ ] NAME:Testes e Refinamento DESCRIPTION:Testar interface Swagger UI, validar exemplos e refinar documentação conforme necessário
--[ ] NAME:Testar interface Swagger UI DESCRIPTION:Acessar /swagger/index.html e verificar funcionalidade
--[ ] NAME:Validar exemplos de request/response DESCRIPTION:Testar exemplos fornecidos na documentação
--[ ] NAME:Refinar documentação DESCRIPTION:Ajustar descrições, exemplos e formatação conforme necessário